#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试扫描方式映射和编码查找
验证CTA、CTV、MRA等是否被正确处理
"""

import pandas as pd
import numpy as np
from streamlit_simple import SimpleMedicalProcessor

def test_scan_mapping():
    """测试扫描方式映射"""
    print("🔬 测试扫描方式映射和编码查找")
    print("="*60)
    
    # 创建处理器
    processor = SimpleMedicalProcessor()
    
    # 加载数据
    excel_file = "NEW_检查项目名称结构表 (8).xlsx"
    success, message = processor.load_data(excel_file)
    
    if not success:
        print(f"❌ 数据加载失败：{message}")
        return
    
    print(f"✅ 数据加载成功")
    
    # 测试CT扫描方式
    print("\n" + "="*40)
    print("CT扫描方式测试")
    print("="*40)
    
    ct_mapping = processor.create_scan_mapping('CT')
    print(f"CT映射表条目数：{len(ct_mapping)}")
    
    # 获取主数据表中的CT列
    ct_columns = [col for col in processor.main_df.columns if col.startswith('CT-')]
    print(f"\n主数据表中的CT扫描方式列：")
    
    for col in ct_columns:
        clean_name = processor.clean_scan_name(col, 'CT')
        scan_code = processor.find_scan_code(clean_name, ct_mapping, 'CT')
        print(f"  {col} -> {clean_name} -> 编码: {scan_code}")
    
    # 测试MR扫描方式
    print("\n" + "="*40)
    print("MR扫描方式测试")
    print("="*40)
    
    mr_mapping = processor.create_scan_mapping('MR')
    print(f"MR映射表条目数：{len(mr_mapping)}")
    
    # 获取主数据表中的MR列
    mr_columns = [col for col in processor.main_df.columns if col.startswith('MR-')]
    print(f"\n主数据表中的MR扫描方式列：")
    
    for col in mr_columns:
        clean_name = processor.clean_scan_name(col, 'MR')
        scan_code = processor.find_scan_code(clean_name, mr_mapping, 'MR')
        print(f"  {col} -> {clean_name} -> 编码: {scan_code}")
    
    # 特别测试CTA、CTV、MRA等
    print("\n" + "="*40)
    print("特殊扫描方式测试")
    print("="*40)
    
    special_tests = [
        ('CT', 'CT-血管成像CTA', 'CTA'),
        ('CT', 'CT-血管成像CTV', 'CTV'),
        ('CT', 'CT-灌注成像', '灌注'),
        ('MR', 'MR-血管成像MRA', 'MRA'),
        ('MR', 'MR-血管成像MRV', 'MRV'),
        ('MR', 'MR-平扫+灌注ASL', 'ASL'),
        ('MR', 'MR-平扫+增强灌注DCE', 'DCE'),
        ('MR', 'MR-水成像MRCP', 'MRCP'),
        ('MR', 'MR-水成像MRU', 'MRU')
    ]
    
    for modality, original, expected_key in special_tests:
        mapping = ct_mapping if modality == 'CT' else mr_mapping
        clean_name = processor.clean_scan_name(original, modality)
        scan_code = processor.find_scan_code(clean_name, mapping, modality)
        
        print(f"  {modality}: {original}")
        print(f"    清理后: {clean_name}")
        print(f"    编码: {scan_code}")
        print(f"    预期包含: {expected_key}")
        
        if expected_key in clean_name:
            print(f"    ✅ 清理正确，保留了{expected_key}")
        else:
            print(f"    ❌ 清理错误，丢失了{expected_key}")
        
        if scan_code != '99':
            print(f"    ✅ 找到编码: {scan_code}")
        else:
            print(f"    ❌ 未找到编码，使用默认: {scan_code}")
        print()
    
    # 测试映射表内容
    print("\n" + "="*40)
    print("CT映射表内容（前20项）")
    print("="*40)
    
    ct_items = list(ct_mapping.items())[:20]
    for key, code in ct_items:
        print(f"  {key} -> {code}")
    
    print("\n" + "="*40)
    print("MR映射表内容（前20项）")
    print("="*40)
    
    mr_items = list(mr_mapping.items())[:20]
    for key, code in mr_items:
        print(f"  {key} -> {code}")
    
    # 统计未匹配的项目
    print("\n" + "="*40)
    print("匹配统计")
    print("="*40)
    
    ct_unmatched = []
    for col in ct_columns:
        clean_name = processor.clean_scan_name(col, 'CT')
        scan_code = processor.find_scan_code(clean_name, ct_mapping, 'CT')
        if scan_code == '99':
            ct_unmatched.append((col, clean_name))
    
    mr_unmatched = []
    for col in mr_columns:
        clean_name = processor.clean_scan_name(col, 'MR')
        scan_code = processor.find_scan_code(clean_name, mr_mapping, 'MR')
        if scan_code == '99':
            mr_unmatched.append((col, clean_name))
    
    print(f"CT扫描方式总数: {len(ct_columns)}")
    print(f"CT未匹配数: {len(ct_unmatched)}")
    print(f"CT匹配率: {(len(ct_columns) - len(ct_unmatched)) / len(ct_columns) * 100:.1f}%")
    
    if ct_unmatched:
        print("\nCT未匹配项目:")
        for original, clean in ct_unmatched:
            print(f"  {original} -> {clean}")
    
    print(f"\nMR扫描方式总数: {len(mr_columns)}")
    print(f"MR未匹配数: {len(mr_unmatched)}")
    print(f"MR匹配率: {(len(mr_columns) - len(mr_unmatched)) / len(mr_columns) * 100:.1f}%")
    
    if mr_unmatched:
        print("\nMR未匹配项目:")
        for original, clean in mr_unmatched:
            print(f"  {original} -> {clean}")

def test_three_level_dict():
    """测试三级部位字典表"""
    print("\n" + "="*60)
    print("🏗️ 测试三级部位字典表")
    print("="*60)
    
    processor = SimpleMedicalProcessor()
    success, message = processor.load_data("NEW_检查项目名称结构表 (8).xlsx")
    
    if not success:
        print(f"❌ 数据加载失败：{message}")
        return
    
    three_level_dict = processor.generate_three_level_dict()
    
    if three_level_dict is not None:
        print(f"✅ 成功生成三级部位字典表：{len(three_level_dict)}条记录")
        
        # 检查二级部位是否有数据
        non_empty_second_level = three_level_dict['二级部位'].notna().sum()
        print(f"有二级部位数据的记录数：{non_empty_second_level}")
        
        # 显示前10条记录
        print("\n前10条记录：")
        print(three_level_dict[['一级部位', '二级部位', '三级部位', '部位编码']].head(10).to_string(index=False))
        
        # 检查二级部位的唯一值
        unique_second_levels = three_level_dict['二级部位'].dropna().unique()
        print(f"\n二级部位唯一值数量：{len(unique_second_levels)}")
        print("前10个二级部位：")
        for i, level in enumerate(unique_second_levels[:10]):
            print(f"  {i+1}. {level}")
    else:
        print("❌ 无法生成三级部位字典表")

if __name__ == "__main__":
    test_scan_mapping()
    test_three_level_dict()
    print("\n🎉 测试完成！")
