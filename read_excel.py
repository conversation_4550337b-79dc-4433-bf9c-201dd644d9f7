import pandas as pd
import sys

try:
    # 读取Excel文件
    excel_file = pd.ExcelFile('/Users/<USER>/Desktop/12-new/DR项目结构-0705.xlsx')
    
    print('=== Excel文件结构分析 ===')
    print(f'工作表数量: {len(excel_file.sheet_names)}')
    print(f'工作表名称: {excel_file.sheet_names}')
    
    # 分析每个工作表
    for sheet_name in excel_file.sheet_names:
        print(f'\n=== 工作表: {sheet_name} ===')
        df = pd.read_excel('/Users/<USER>/Desktop/12-new/DR项目结构-0705.xlsx', sheet_name=sheet_name)
        
        print(f'数据形状: {df.shape} (行数: {df.shape[0]}, 列数: {df.shape[1]})')
        
        if sheet_name == 'Sheet1':
            print(f'\n=== 主要列信息 ===')
            main_cols = ['一级部位', '二级部位', '三级部位', '名称', '摆位']
            for col in main_cols:
                if col in df.columns:
                    unique_count = df[col].nunique()
                    non_null_count = df[col].notna().sum()
                    print(f'{col}: {non_null_count}/{len(df)} 非空, {unique_count} 个唯一值')
                    if unique_count <= 20:
                        print(f'  唯一值: {df[col].dropna().unique().tolist()}')
            
            print(f'\n=== 前5行主要数据 ===')
            if len(main_cols) > 0:
                available_cols = [col for col in main_cols if col in df.columns]
                print(df[available_cols].head().to_string(index=False))
            
            print(f'\n=== 摆位相关列统计 ===')
            position_cols = [col for col in df.columns if col not in main_cols and df[col].notna().sum() > 0]
            position_stats = []
            for col in position_cols:
                non_null_count = df[col].notna().sum()
                if non_null_count > 0:
                    position_stats.append((col, non_null_count))
            
            # 按非空数量排序
            position_stats.sort(key=lambda x: x[1], reverse=True)
            print('摆位类型使用频率 (前20个):')
            for col, count in position_stats[:20]:
                print(f'  {col}: {count} 次')
        
        elif sheet_name == 'Sheet2':
            print(f'\n=== Sheet2 列信息 ===')
            for i, col in enumerate(df.columns[:10]):
                print(f'{i+1}. {col}')
            if len(df.columns) > 10:
                print(f'... 还有 {len(df.columns) - 10} 列')
            
            print(f'\n=== Sheet2 前3行数据 ===')
            print(df.head(3).to_string(index=False))
    
except Exception as e:
    print(f'读取Excel文件时出错: {e}')
    import traceback
    traceback.print_exc()
    sys.exit(1)