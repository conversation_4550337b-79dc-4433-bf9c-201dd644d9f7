# 检查项目生成规则 V2.0

## 项目生成要求总结

### 1. 基本要求
- **按模态分别生成**: 分别生成CT和MR的检查项目清单
- **扫描方式编码**: 采用两位编码，从CT扫描方式、MR扫描方式工作表中匹配
- **字段清理**: 去除多余空格，扫描方式去除重复字符
- **字典表生成**: 生成每个字段的字典表说明

### 2. 数据源结构
- **主数据表**: `三级部位结构` - 包含部位层级和模态配置
- **CT扫描方式表**: `CT扫描方式` - CT扫描方式编码对照
- **MR扫描方式表**: `MR扫描方式` - MR扫描方式编码对照

### 3. 生成格式规则

#### 3.1 项目名称格式
```
格式: "[模态][部位]([扫描方式])"
示例: 
- CT颅脑(平扫)
- MR颅脑(增强)
- CT四肢及关节(平扫+增强)
```

#### 3.2 项目编码格式
```
格式: "[模态][部位编码][扫描编码]"
长度: 10位字符
示例:
- CT01010110 (CT + 010101 + 10)
- MR01010120 (MR + 010101 + 20)
```

#### 3.3 字段清理规则
- **扫描方式清理**:
  - 去除 `CT-`、`CT_`、`CT` 前缀
  - 去除 `MR-`、`MR_`、`MR` 前缀
  - 去除多余空格
- **部位名称清理**:
  - 去除多余空格
  - 保持原始中文名称

### 4. 编码映射规则

#### 4.1 CT扫描方式编码
基于 `CT扫描方式` 工作表，包含：
- CT扫描分类编码
- CT扫描分类名称
- CT扫描编码 (两位)
- CT扫描名称

#### 4.2 MR扫描方式编码
基于 `MR扫描方式` 工作表，包含：
- MR成像分类编码
- MR成像分类
- MR成像编码 (两位)
- MR成像名称

#### 4.3 默认编码规则
当无法匹配具体编码时，使用默认编码：
- **CT默认编码**:
  - 平扫: 10
  - 增强: 20
  - CTA: 30
  - CTV: 31
  - 灌注: 40
- **MR默认编码**:
  - 平扫: 10
  - 增强: 20
  - MRA: 30
  - MRV: 31
- **未知类型**: 99

### 5. 生成结果统计

#### 5.1 项目数量统计
- **CT项目**: 333个
- **MR项目**: 555个
- **总计**: 888个

#### 5.2 覆盖范围统计
- **部位覆盖**: 9个一级部位
- **CT扫描方式**: 7种
- **MR扫描方式**: 16种

### 6. 输出文件结构

#### 6.1 Excel工作表
- **CT检查项目清单**: 完整的CT项目列表
- **MR检查项目清单**: 完整的MR项目列表
- **字段字典**: 所有字段的定义和说明
- **CT统计**: CT项目的统计分析
- **MR统计**: MR项目的统计分析

#### 6.2 字段字典内容
| 字段名 | 字段描述 | 数据类型 | 取值范围 | 示例值 |
|--------|----------|----------|----------|--------|
| 一级部位 | 一级部位名称 | 文本 | 头部、四肢及关节、血管等 | 头部 |
| 二级编码 | 二级部位编码 | 文本 | 01-99 | 01 |
| 三级编码 | 三级部位编码 | 文本 | 01-99 | 01 |
| 三级部位 | 三级部位名称 | 文本 | 具体部位名称 | 颅脑 |
| 部位编码 | 六位部位编码 | 文本 | 010101-999999 | 010101 |
| 扫描方式原名 | 原始扫描方式名称 | 文本 | 数据表中的原始列名 | CT-平扫 |
| 扫描方式清理名 | 清理后扫描方式名称 | 文本 | 去除前缀后的名称 | 平扫 |
| 扫描编码 | 两位扫描编码 | 文本 | 10-99 | 10 |
| 检查项目名称 | 最终生成的项目名称 | 文本 | 模态+部位+(扫描) | CT颅脑(平扫) |
| 检查项目编码 | 最终生成的项目编码 | 文本 | 模态+部位编码+扫描编码 | CT01010110 |
| 检查模态 | 检查模态类型 | 文本 | CT、MR | CT |

### 7. 项目示例

#### 7.1 CT项目示例
```
CT01010110 - CT颅脑(平扫)
CT01010120 - CT颅脑(增强)
CT01010130 - CT颅脑(平扫+增强)
CT01010125 - CT颅脑(灌注)
CT01010210 - CT颅脑[外伤](平扫)
```

#### 7.2 MR项目示例
```
MR01010110 - MR颅脑(平扫)
MR01010120 - MR颅脑(增强)
MR01010130 - MR颅脑(平扫+增强)
MR01010181 - MR颅脑(平扫+ASL)
MR01010110 - MR颅脑(平扫+DCE)
```

### 8. 生成流程

#### 8.1 数据加载
1. 读取主数据表（三级部位结构）
2. 读取CT扫描方式表
3. 读取MR扫描方式表
4. 创建扫描方式映射关系

#### 8.2 CT项目生成
1. 遍历主数据表中CT标记为"1"的行
2. 检查所有CT相关列（CT-平扫、CT-增强等）
3. 对于值为1.0的列，生成对应的检查项目
4. 匹配扫描方式编码
5. 生成项目名称和编码

#### 8.3 MR项目生成
1. 遍历主数据表中MR标记为"1"的行
2. 检查所有MR相关列（MR-平扫、MR-增强等）
3. 对于值为1.0的列，生成对应的检查项目
4. 匹配扫描方式编码
5. 生成项目名称和编码

#### 8.4 结果输出
1. 生成字段字典
2. 生成统计信息
3. 导出Excel文件
4. 输出生成摘要

### 9. 质量控制

#### 9.1 数据验证
- 检查NaN值处理
- 验证编码格式一致性
- 确保扫描方式匹配准确性

#### 9.2 格式验证
- 项目名称格式正确性
- 编码长度和格式验证
- 字符清理效果检查

#### 9.3 完整性检查
- 所有有效项目都已生成
- 统计数据准确性
- 字段字典完整性

### 10. 使用说明

#### 10.1 运行生成器
```bash
python3 generate_check_items_v2.py
```

#### 10.2 自定义配置
可以通过修改以下方法来调整生成规则：
- `clean_scan_name()`: 修改扫描方式清理规则
- `find_scan_code()`: 修改编码匹配规则
- `generate_field_dictionary()`: 修改字段字典内容

#### 10.3 输出文件
- 文件名格式: `检查项目生成结果_V2_YYYYMMDD_HHMMSS.xlsx`
- 包含5个工作表: CT清单、MR清单、字段字典、CT统计、MR统计

### 11. 注意事项

1. **数据源依赖**: 确保Excel文件包含所有必要的工作表
2. **编码唯一性**: 生成的编码应在系统中保持唯一性
3. **版本控制**: 建议对生成结果进行版本管理
4. **数据更新**: 当源数据更新时，需重新运行生成器
5. **格式标准**: 确保生成的名称符合医疗行业标准

这套规则确保了检查项目名称和编码的标准化、一致性和可维护性。 