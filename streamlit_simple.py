#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
医学检查项目处理流程 - 简化版Streamlit应用
专注于核心功能，避免复杂图表问题
"""

import streamlit as st
import pandas as pd
import numpy as np
from datetime import datetime
import re
import os
from io import BytesIO

# 设置页面配置
st.set_page_config(
    page_title="医学检查项目处理流程",
    page_icon="🏥",
    layout="wide",
    initial_sidebar_state="expanded"
)

class SimpleMedicalProcessor:
    """简化版医学检查项目处理器"""
    
    def __init__(self):
        self.main_df = None
        self.ct_scan_df = None
        self.mr_scan_df = None
        
    def load_data(self, uploaded_file):
        """加载Excel数据"""
        try:
            self.main_df = pd.read_excel(uploaded_file, sheet_name='三级部位结构')
            self.ct_scan_df = pd.read_excel(uploaded_file, sheet_name='CT扫描方式')
            self.mr_scan_df = pd.read_excel(uploaded_file, sheet_name='MR扫描方式')
            return True, "数据加载成功"
        except Exception as e:
            return False, f"数据加载失败：{str(e)}"
    
    def get_basic_stats(self):
        """获取基本统计信息"""
        if self.main_df is None:
            return None
        
        stats = {
            'main_rows': len(self.main_df),
            'main_cols': len(self.main_df.columns),
            'missing_values': int(self.main_df.isnull().sum().sum()),
            'ct_scans': len(self.ct_scan_df) if self.ct_scan_df is not None else 0,
            'mr_scans': len(self.mr_scan_df) if self.mr_scan_df is not None else 0,
            'ct_parts': len(self.main_df[self.main_df['CT'].astype(str).str.strip().isin(['1', '１'])]) if 'CT' in self.main_df.columns else 0,
            'mr_parts': len(self.main_df[self.main_df['MR'].astype(str).str.strip().isin(['1', '１'])]) if 'MR' in self.main_df.columns else 0
        }
        return stats
    
    def clean_scan_name(self, scan_name, modality):
        """清理扫描方式名称"""
        if pd.isna(scan_name):
            return ""
        
        scan_name = str(scan_name).strip()
        
        if modality == 'CT':
            scan_name = scan_name.replace('CT-', '').replace('CT_', '').replace('CT', '')
        elif modality == 'MR':
            scan_name = scan_name.replace('MR-', '').replace('MR_', '').replace('MR', '')
        
        return re.sub(r'\s+', ' ', scan_name).strip()
    
    def generate_three_level_dict(self):
        """生成三级部位字典表"""
        if self.main_df is None:
            return None
            
        dict_data = []
        for _, row in self.main_df.iterrows():
            part_code = str(row.get('部位编码', '')).strip()
            if len(part_code) == 6 and part_code.isdigit():
                dict_item = {
                    '一级编码': row.get('一级编码', ''),
                    '一级部位': str(row.get('一级部位', '')).strip(),
                    '二级编码': row.get('二级编码', ''),
                    '二级部位': str(row.get('二级合并', '')).strip(),
                    '三级编码': row.get('三级编码', ''),
                    '三级部位': str(row.get('三级部位', '')).strip(),
                    '部位编码': part_code,
                    'CT适用': '是' if str(row.get('CT', '')).strip() in ['1', '１'] else '否',
                    'MR适用': '是' if str(row.get('MR', '')).strip() in ['1', '１'] else '否'
                }
                dict_data.append(dict_item)
        
        return pd.DataFrame(dict_data)
    
    def generate_scan_dict(self, modality):
        """生成扫描方式字典表"""
        if modality == 'CT' and self.ct_scan_df is not None:
            scan_dict = []
            for _, row in self.ct_scan_df.iterrows():
                scan_name = row.get('CT扫描名称', '')
                scan_code = row.get('CT扫描编码', '')
                if pd.notna(scan_name) and pd.notna(scan_code):
                    clean_name = self.clean_scan_name(scan_name, 'CT')
                    dict_item = {
                        '扫描编码': str(scan_code).zfill(2),
                        '扫描名称': scan_name,
                        '清理后名称': clean_name
                    }
                    scan_dict.append(dict_item)
            return pd.DataFrame(scan_dict)
            
        elif modality == 'MR' and self.mr_scan_df is not None:
            scan_dict = []
            for _, row in self.mr_scan_df.iterrows():
                scan_name = row.get('MR成像名称', '')
                scan_code = row.get('MR成像编码', '')
                if pd.notna(scan_name) and pd.notna(scan_code):
                    clean_name = self.clean_scan_name(scan_name, 'MR')
                    dict_item = {
                        '成像编码': str(scan_code).zfill(2),
                        '成像名称': scan_name,
                        '清理后名称': clean_name
                    }
                    scan_dict.append(dict_item)
            return pd.DataFrame(scan_dict)
        
        return None
    
    def generate_check_items(self, modality):
        """生成检查项目"""
        if self.main_df is None:
            return None
        
        items = []
        mapping = self.create_scan_mapping(modality)
        
        # 获取相关列
        if modality == 'CT':
            scan_columns = [col for col in self.main_df.columns if col.startswith('CT') and col != 'CT']
        else:
            scan_columns = [col for col in self.main_df.columns if col.startswith('MR') and col != 'MR']
        
        for _, row in self.main_df.iterrows():
            if pd.notna(row.get(modality)) and str(row.get(modality)).strip() in ['1', '１']:
                part_name = str(row['三级部位']).strip()
                part_code = str(row['部位编码']).strip()
                
                for scan_col in scan_columns:
                    if pd.notna(row[scan_col]) and row[scan_col] == 1.0:
                        scan_raw_name = scan_col
                        scan_clean_name = self.clean_scan_name(scan_raw_name, modality)
                        scan_code = self.find_scan_code(scan_clean_name, mapping, modality)
                        
                        item_name = f"{modality}{part_name}({scan_clean_name})"
                        item_code = f"{modality}{part_code}{scan_code}".replace(' ', '')
                        
                        item = {
                            '一级部位': row['一级部位'],
                            '三级部位': part_name,
                            '部位编码': part_code,
                            '扫描方式清理名': scan_clean_name,
                            '扫描编码': scan_code,
                            '检查项目名称': item_name,
                            '检查项目编码': item_code,
                            '检查模态': modality
                        }
                        items.append(item)
        
        return pd.DataFrame(items)
    
    def create_scan_mapping(self, modality):
        """创建扫描方式映射表"""
        mapping = {}
        
        if modality == 'CT' and self.ct_scan_df is not None:
            for _, row in self.ct_scan_df.iterrows():
                scan_name = row.get('CT扫描名称', '')
                scan_code = row.get('CT扫描编码', '')
                if pd.notna(scan_name) and pd.notna(scan_code):
                    mapping[scan_name] = str(scan_code).zfill(2)
        
        elif modality == 'MR' and self.mr_scan_df is not None:
            for _, row in self.mr_scan_df.iterrows():
                scan_name = row.get('MR成像名称', '')
                scan_code = row.get('MR成像编码', '')
                if pd.notna(scan_name) and pd.notna(scan_code):
                    mapping[scan_name] = str(scan_code).zfill(2)
        
        return mapping
    
    def find_scan_code(self, scan_clean_name, mapping, modality):
        """查找扫描编码"""
        # 直接匹配
        for key, code in mapping.items():
            if scan_clean_name in key or key in scan_clean_name:
                return code
        
        # 默认编码规则
        if modality == 'CT':
            default_codes = {
                '平扫': '10', '增强': '20', 'CTA': '30', 'CTV': '31', '灌注': '40'
            }
        else:
            default_codes = {
                '平扫': '10', '增强': '20', 'MRA': '30', 'MRV': '31'
            }
        
        for key, code in default_codes.items():
            if key in scan_clean_name:
                return code
        
        return '99'

def main():
    """主函数"""
    st.title("🏥 医学检查项目名称和编码处理流程")
    st.markdown("---")
    
    # 初始化处理器
    if 'processor' not in st.session_state:
        st.session_state.processor = SimpleMedicalProcessor()
    
    # 侧边栏 - 文件上传
    st.sidebar.header("📁 数据源")
    
    # 检查默认文件
    default_file = "NEW_检查项目名称结构表 (8).xlsx"
    if os.path.exists(default_file):
        if st.sidebar.button("使用默认数据文件"):
            success, message = st.session_state.processor.load_data(default_file)
            if success:
                st.sidebar.success(message)
                st.session_state.data_loaded = True
            else:
                st.sidebar.error(message)
    
    # 文件上传
    uploaded_file = st.sidebar.file_uploader(
        "上传Excel文件",
        type=['xlsx', 'xls'],
        help="请上传包含'三级部位结构'、'CT扫描方式'、'MR扫描方式'工作表的Excel文件"
    )
    
    if uploaded_file is not None:
        success, message = st.session_state.processor.load_data(uploaded_file)
        if success:
            st.sidebar.success(message)
            st.session_state.data_loaded = True
        else:
            st.sidebar.error(message)
            st.session_state.data_loaded = False
    
    # 主界面
    if not hasattr(st.session_state, 'data_loaded') or not st.session_state.data_loaded:
        st.info("请在侧边栏上传Excel数据文件或使用默认文件开始处理")
        return
    
    # 步骤选择
    st.sidebar.header("📋 处理步骤")
    step = st.sidebar.selectbox(
        "选择处理步骤",
        [
            "1. 数据加载与分析",
            "2. 三级部位字典表生成",
            "3. 扫描方式字典表生成",
            "4. 检查项目清单生成",
            "5. 数据分析与质量控制"
        ]
    )
    
    processor = st.session_state.processor
    
    if step == "1. 数据加载与分析":
        st.header("📊 步骤1：数据加载与分析")
        
        stats = processor.get_basic_stats()
        if stats:
            col1, col2, col3, col4 = st.columns(4)
            
            with col1:
                st.metric("主数据表行数", stats['main_rows'])
                st.metric("主数据表列数", stats['main_cols'])
            
            with col2:
                st.metric("CT扫描方式数", stats['ct_scans'])
                st.metric("MR扫描方式数", stats['mr_scans'])
            
            with col3:
                st.metric("CT适用部位", stats['ct_parts'])
                st.metric("MR适用部位", stats['mr_parts'])
            
            with col4:
                st.metric("缺失值总数", stats['missing_values'])
            
            # 数据预览
            st.subheader("数据预览")
            tab1, tab2, tab3 = st.tabs(["三级部位结构", "CT扫描方式", "MR扫描方式"])
            
            with tab1:
                if processor.main_df is not None:
                    st.dataframe(processor.main_df.head(10), use_container_width=True)
            
            with tab2:
                if processor.ct_scan_df is not None:
                    st.dataframe(processor.ct_scan_df, use_container_width=True)
            
            with tab3:
                if processor.mr_scan_df is not None:
                    st.dataframe(processor.mr_scan_df, use_container_width=True)
    
    elif step == "2. 三级部位字典表生成":
        st.header("🏗️ 步骤2：三级部位字典表生成")
        
        three_level_dict = processor.generate_three_level_dict()
        
        if three_level_dict is not None:
            st.success(f"成功生成三级部位字典表，共 {len(three_level_dict)} 条记录")
            
            # 搜索功能
            search_term = st.text_input("搜索部位名称")
            if search_term:
                filtered_dict = three_level_dict[
                    three_level_dict['一级部位'].str.contains(search_term, na=False) |
                    three_level_dict['二级部位'].str.contains(search_term, na=False) |
                    three_level_dict['三级部位'].str.contains(search_term, na=False)
                ]
                st.dataframe(filtered_dict, use_container_width=True)
            else:
                st.dataframe(three_level_dict, use_container_width=True)
            
            # 数据质量检查
            st.subheader("数据质量检查")
            col1, col2, col3 = st.columns(3)
            
            with col1:
                valid_codes = (three_level_dict['部位编码'].str.len() == 6).sum()
                st.metric("有效编码数", valid_codes)
            
            with col2:
                duplicate_codes = three_level_dict['部位编码'].duplicated().sum()
                st.metric("重复编码数", duplicate_codes)
            
            with col3:
                empty_names = three_level_dict['三级部位'].isna().sum()
                st.metric("空名称数", empty_names)
            
            # 下载功能
            if st.button("下载三级部位字典表"):
                csv = three_level_dict.to_csv(index=False, encoding='utf-8-sig')
                st.download_button(
                    label="下载CSV文件",
                    data=csv,
                    file_name=f"三级部位字典表_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                    mime="text/csv"
                )
        else:
            st.error("无法生成三级部位字典表")
    
    elif step == "3. 扫描方式字典表生成":
        st.header("🔬 步骤3：扫描方式字典表生成")
        
        modality = st.selectbox("选择检查模态", ["CT", "MR"])
        scan_dict = processor.generate_scan_dict(modality)
        
        if scan_dict is not None:
            st.success(f"成功生成{modality}扫描方式字典表，共 {len(scan_dict)} 条记录")
            
            # 显示清理前后对比
            st.subheader("扫描方式清理效果")
            if modality == "CT":
                comparison_cols = ['扫描名称', '清理后名称']
            else:
                comparison_cols = ['成像名称', '清理后名称']
            
            st.dataframe(scan_dict[comparison_cols], use_container_width=True)
            
            # 完整字典表
            st.subheader(f"{modality}扫描方式字典表")
            st.dataframe(scan_dict, use_container_width=True)
            
            # 下载功能
            if st.button(f"下载{modality}扫描方式字典表"):
                csv = scan_dict.to_csv(index=False, encoding='utf-8-sig')
                st.download_button(
                    label="下载CSV文件",
                    data=csv,
                    file_name=f"{modality}扫描方式字典表_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                    mime="text/csv"
                )
        else:
            st.error(f"无法生成{modality}扫描方式字典表")
    
    elif step == "4. 检查项目清单生成":
        st.header("📋 步骤4：检查项目清单生成")
        
        with st.spinner("正在生成检查项目..."):
            ct_items = processor.generate_check_items('CT')
            mr_items = processor.generate_check_items('MR')
        
        if ct_items is not None and mr_items is not None:
            # 显示生成结果统计
            col1, col2, col3 = st.columns(3)
            with col1:
                st.metric("CT项目数量", len(ct_items))
            with col2:
                st.metric("MR项目数量", len(mr_items))
            with col3:
                st.metric("总项目数量", len(ct_items) + len(mr_items))
            
            # 选择查看的项目类型
            item_type = st.selectbox("选择查看项目类型", ["CT检查项目", "MR检查项目"])
            
            if item_type == "CT检查项目":
                st.subheader("CT检查项目清单")
                st.dataframe(ct_items, use_container_width=True)
                
                # CT项目示例
                if len(ct_items) > 0:
                    st.subheader("CT项目示例")
                    for i, (_, row) in enumerate(ct_items.head(3).iterrows()):
                        st.code(f"{row['检查项目编码']} - {row['检查项目名称']}")
            
            else:
                st.subheader("MR检查项目清单")
                st.dataframe(mr_items, use_container_width=True)
                
                # MR项目示例
                if len(mr_items) > 0:
                    st.subheader("MR项目示例")
                    for i, (_, row) in enumerate(mr_items.head(3).iterrows()):
                        st.code(f"{row['检查项目编码']} - {row['检查项目名称']}")
            
            # 导出Excel功能
            if st.button("生成完整Excel报告"):
                try:
                    buffer = BytesIO()
                    
                    with pd.ExcelWriter(buffer, engine='openpyxl') as writer:
                        ct_items.to_excel(writer, sheet_name='CT检查项目清单', index=False)
                        mr_items.to_excel(writer, sheet_name='MR检查项目清单', index=False)
                        
                        # 三级部位字典
                        three_level_dict = processor.generate_three_level_dict()
                        if three_level_dict is not None:
                            three_level_dict.to_excel(writer, sheet_name='三级部位字典', index=False)
                        
                        # 扫描方式字典
                        ct_scan_dict = processor.generate_scan_dict('CT')
                        mr_scan_dict = processor.generate_scan_dict('MR')
                        if ct_scan_dict is not None:
                            ct_scan_dict.to_excel(writer, sheet_name='CT扫描方式字典', index=False)
                        if mr_scan_dict is not None:
                            mr_scan_dict.to_excel(writer, sheet_name='MR扫描方式字典', index=False)
                    
                    buffer.seek(0)
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    st.download_button(
                        label="下载Excel报告",
                        data=buffer.getvalue(),
                        file_name=f"检查项目清单_{timestamp}.xlsx",
                        mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                    )
                    
                except Exception as e:
                    st.error(f"生成Excel报告失败：{str(e)}")
        else:
            st.error("检查项目生成失败")
    
    elif step == "5. 数据分析与质量控制":
        st.header("🔍 步骤5：数据分析与质量控制")
        
        ct_items = processor.generate_check_items('CT')
        mr_items = processor.generate_check_items('MR')
        
        if ct_items is not None and mr_items is not None:
            # 综合统计报告
            st.subheader("综合统计报告")
            
            stats_data = {
                "统计项目": [
                    "CT项目总数", "MR项目总数", "总项目数",
                    "涉及一级部位数", "涉及CT扫描方式数", "涉及MR扫描方式数"
                ],
                "统计值": [
                    len(ct_items), len(mr_items), len(ct_items) + len(mr_items),
                    len(ct_items['一级部位'].unique()) if len(ct_items) > 0 else 0,
                    len(ct_items['扫描方式清理名'].unique()) if len(ct_items) > 0 else 0,
                    len(mr_items['扫描方式清理名'].unique()) if len(mr_items) > 0 else 0
                ]
            }
            
            stats_df = pd.DataFrame(stats_data)
            st.dataframe(stats_df, use_container_width=True)
            
            # 错误分析
            st.subheader("错误分析")
            
            errors = []
            
            # 检查编码格式错误
            if len(ct_items) > 0:
                invalid_ct_codes = ct_items[ct_items['检查项目编码'].str.len() != 10]
                if len(invalid_ct_codes) > 0:
                    errors.append(f"CT编码格式错误: {len(invalid_ct_codes)}项")
            
            if len(mr_items) > 0:
                invalid_mr_codes = mr_items[mr_items['检查项目编码'].str.len() != 10]
                if len(invalid_mr_codes) > 0:
                    errors.append(f"MR编码格式错误: {len(invalid_mr_codes)}项")
            
            # 检查重复编码
            all_codes = []
            if len(ct_items) > 0:
                all_codes.extend(ct_items['检查项目编码'].tolist())
            if len(mr_items) > 0:
                all_codes.extend(mr_items['检查项目编码'].tolist())
            
            duplicate_codes = len(all_codes) - len(set(all_codes))
            if duplicate_codes > 0:
                errors.append(f"重复编码: {duplicate_codes}项")
            
            if errors:
                for error in errors:
                    st.error(error)
            else:
                st.success("未发现数据质量问题")
            
            # 数据完整性验证
            st.subheader("数据完整性验证")
            
            completeness_checks = []
            
            if len(ct_items) > 0:
                ct_missing = ct_items.isnull().sum().sum()
                completeness_checks.append(("CT项目数据", "完整" if ct_missing == 0 else f"缺失{ct_missing}个值"))
            
            if len(mr_items) > 0:
                mr_missing = mr_items.isnull().sum().sum()
                completeness_checks.append(("MR项目数据", "完整" if mr_missing == 0 else f"缺失{mr_missing}个值"))
            
            completeness_df = pd.DataFrame(completeness_checks, columns=["检查项", "状态"])
            st.dataframe(completeness_df, use_container_width=True)
        else:
            st.error("无法进行质量控制分析")

if __name__ == "__main__":
    main()
