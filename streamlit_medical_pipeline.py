#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
医学检查项目名称和编码处理流程 - Streamlit应用
基于项目规则文件构建的标准化处理pipeline
"""

import streamlit as st
import pandas as pd
import numpy as np
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime
import re
import os
from io import BytesIO

# 设置页面配置
st.set_page_config(
    page_title="医学检查项目处理流程",
    page_icon="🏥",
    layout="wide",
    initial_sidebar_state="expanded"
)

class MedicalItemProcessor:
    """医学检查项目处理器"""

    def __init__(self):
        self.main_df = None
        self.ct_scan_df = None
        self.mr_scan_df = None
        self.excel_file = None

    def load_data(self, uploaded_file):
        """加载Excel数据"""
        try:
            # 加载主数据表
            self.main_df = pd.read_excel(uploaded_file, sheet_name='三级部位结构')

            # 加载CT扫描方式表
            self.ct_scan_df = pd.read_excel(uploaded_file, sheet_name='CT扫描方式')

            # 加载MR扫描方式表
            self.mr_scan_df = pd.read_excel(uploaded_file, sheet_name='MR扫描方式')

            return True, "数据加载成功"
        except Exception as e:
            return False, f"数据加载失败：{str(e)}"

    def analyze_data(self):
        """数据基本分析"""
        if self.main_df is None:
            return None

        # 处理数据类型统计，确保可序列化
        dtype_counts = self.main_df.dtypes.value_counts()
        clean_data_types = {}
        for dtype, count in dtype_counts.items():
            clean_data_types[str(dtype)] = int(count)

        analysis = {
            'main_data': {
                'rows': len(self.main_df),
                'columns': len(self.main_df.columns),
                'missing_values': int(self.main_df.isnull().sum().sum()),
                'data_types': clean_data_types
            },
            'ct_scan_data': {
                'rows': len(self.ct_scan_df) if self.ct_scan_df is not None else 0,
                'columns': len(self.ct_scan_df.columns) if self.ct_scan_df is not None else 0
            },
            'mr_scan_data': {
                'rows': len(self.mr_scan_df) if self.mr_scan_df is not None else 0,
                'columns': len(self.mr_scan_df.columns) if self.mr_scan_df is not None else 0
            }
        }
        return analysis

    def clean_scan_name(self, scan_name, modality):
        """清理扫描方式名称"""
        if pd.isna(scan_name):
            return ""

        scan_name = str(scan_name).strip()

        if modality == 'CT':
            # 去除CT前缀
            scan_name = scan_name.replace('CT-', '').replace('CT_', '').replace('CT', '')
        elif modality == 'MR':
            # 去除MR前缀
            scan_name = scan_name.replace('MR-', '').replace('MR_', '').replace('MR', '')

        # 去除多余空格
        return re.sub(r'\s+', ' ', scan_name).strip()

    def clean_part_name(self, part_name):
        """清理部位名称"""
        if pd.isna(part_name):
            return ""
        return str(part_name).strip()

    def generate_three_level_dict(self):
        """生成三级部位字典表"""
        if self.main_df is None:
            return None

        # 清洗和验证数据
        dict_data = []
        for _, row in self.main_df.iterrows():
            # 验证部位编码格式（六位数字）
            part_code = str(row.get('部位编码', '')).strip()
            if len(part_code) == 6 and part_code.isdigit():
                dict_item = {
                    '一级编码': row.get('一级编码', ''),
                    '一级部位': self.clean_part_name(row.get('一级部位', '')),
                    '二级编码': row.get('二级编码', ''),
                    '二级部位': self.clean_part_name(row.get('二级合并', '')),
                    '三级编码': row.get('三级编码', ''),
                    '三级部位': self.clean_part_name(row.get('三级部位', '')),
                    '部位编码': part_code,
                    'CT适用': '是' if str(row.get('CT', '')).strip() in ['1', '１'] else '否',
                    'MR适用': '是' if str(row.get('MR', '')).strip() in ['1', '１'] else '否'
                }
                dict_data.append(dict_item)

        return pd.DataFrame(dict_data)

    def generate_scan_dict(self, modality):
        """生成扫描方式字典表"""
        if modality == 'CT' and self.ct_scan_df is not None:
            scan_dict = []
            for _, row in self.ct_scan_df.iterrows():
                scan_name = row.get('CT扫描名称', '')
                scan_code = row.get('CT扫描编码', '')
                if pd.notna(scan_name) and pd.notna(scan_code):
                    clean_name = self.clean_scan_name(scan_name, 'CT')
                    dict_item = {
                        '扫描分类编码': row.get('CT扫描分类编码', ''),
                        '扫描分类名称': row.get('CT扫描分类名称', ''),
                        '扫描编码': str(scan_code).zfill(2),
                        '扫描名称': scan_name,
                        '清理后名称': clean_name
                    }
                    scan_dict.append(dict_item)
            return pd.DataFrame(scan_dict)

        elif modality == 'MR' and self.mr_scan_df is not None:
            scan_dict = []
            for _, row in self.mr_scan_df.iterrows():
                scan_name = row.get('MR成像名称', '')
                scan_code = row.get('MR成像编码', '')
                if pd.notna(scan_name) and pd.notna(scan_code):
                    clean_name = self.clean_scan_name(scan_name, 'MR')
                    dict_item = {
                        '成像分类编码': row.get('MR成像分类编码', ''),
                        '成像分类': row.get('MR成像分类', ''),
                        '成像编码': str(scan_code).zfill(2),
                        '成像名称': scan_name,
                        '清理后名称': clean_name
                    }
                    scan_dict.append(dict_item)
            return pd.DataFrame(scan_dict)

        return None

def main():
    """主函数"""
    st.title("🏥 医学检查项目名称和编码处理流程")
    st.markdown("---")

    # 初始化处理器
    if 'processor' not in st.session_state:
        st.session_state.processor = MedicalItemProcessor()

    # 侧边栏 - 文件上传
    st.sidebar.header("📁 数据源")

    # 检查是否有默认文件
    default_file = "NEW_检查项目名称结构表 (8).xlsx"
    if os.path.exists(default_file):
        if st.sidebar.button("使用默认数据文件"):
            success, message = st.session_state.processor.load_data(default_file)
            if success:
                st.sidebar.success(message)
                st.session_state.data_loaded = True
            else:
                st.sidebar.error(message)

    # 文件上传
    uploaded_file = st.sidebar.file_uploader(
        "上传Excel文件",
        type=['xlsx', 'xls'],
        help="请上传包含'三级部位结构'、'CT扫描方式'、'MR扫描方式'工作表的Excel文件"
    )

    if uploaded_file is not None:
        success, message = st.session_state.processor.load_data(uploaded_file)
        if success:
            st.sidebar.success(message)
            st.session_state.data_loaded = True
        else:
            st.sidebar.error(message)
            st.session_state.data_loaded = False

    # 主界面
    if not hasattr(st.session_state, 'data_loaded') or not st.session_state.data_loaded:
        st.info("请在侧边栏上传Excel数据文件或使用默认文件开始处理")
        return

    # 步骤选择
    st.sidebar.header("📋 处理步骤")
    step = st.sidebar.selectbox(
        "选择处理步骤",
        [
            "1. 数据加载与分析",
            "2. 三级部位字典表生成",
            "3. 扫描方式字典表生成",
            "4. 检查项目清单生成",
            "5. 数据分析与质量控制"
        ]
    )

    # 根据选择的步骤显示相应内容
    if step == "1. 数据加载与分析":
        show_data_analysis()
    elif step == "2. 三级部位字典表生成":
        show_three_level_dict()
    elif step == "3. 扫描方式字典表生成":
        show_scan_dict()
    elif step == "4. 检查项目清单生成":
        show_item_generation()
    elif step == "5. 数据分析与质量控制":
        show_quality_control()

def show_data_analysis():
    """显示数据分析步骤"""
    st.header("📊 步骤1：数据加载与分析")

    processor = st.session_state.processor
    analysis = processor.analyze_data()

    if analysis is None:
        st.error("无法分析数据，请检查数据是否正确加载")
        return

    # 基本统计信息
    col1, col2, col3 = st.columns(3)

    with col1:
        st.metric("主数据表行数", analysis['main_data']['rows'])
        st.metric("主数据表列数", analysis['main_data']['columns'])

    with col2:
        st.metric("CT扫描方式数", analysis['ct_scan_data']['rows'])
        st.metric("MR扫描方式数", analysis['mr_scan_data']['rows'])

    with col3:
        st.metric("缺失值总数", analysis['main_data']['missing_values'])

    # 数据类型分布
    st.subheader("数据类型分布")
    data_types = analysis['main_data']['data_types']

    if data_types:
        # 使用表格显示数据类型分布
        dtype_df = pd.DataFrame([
            {'数据类型': dtype, '字段数量': count}
            for dtype, count in data_types.items()
        ])
        st.dataframe(dtype_df, use_container_width=True)
    else:
        st.info("暂无数据类型分布信息")

    # 部位分布分析
    if processor.main_df is not None:
        st.subheader("一级部位分布")
        part_counts = processor.main_df['一级部位'].value_counts()

        # 确保数据可以被序列化
        if len(part_counts) > 0:
            fig = px.bar(
                x=[str(x) for x in part_counts.index],
                y=[int(y) for y in part_counts.values],
                title="各一级部位项目数量分布",
                labels={'x': '一级部位', 'y': '项目数量'}
            )
            st.plotly_chart(fig, use_container_width=True)
        else:
            st.info("暂无部位分布数据")

        # 模态分布
        st.subheader("检查模态分布")
        col1, col2 = st.columns(2)

        with col1:
            ct_count = processor.main_df[processor.main_df['CT'].astype(str).str.strip().isin(['1', '１'])].shape[0]
            st.metric("CT适用部位数", ct_count)

        with col2:
            mr_count = processor.main_df[processor.main_df['MR'].astype(str).str.strip().isin(['1', '１'])].shape[0]
            st.metric("MR适用部位数", mr_count)

    # 数据预览
    st.subheader("数据预览")
    tab1, tab2, tab3 = st.tabs(["三级部位结构", "CT扫描方式", "MR扫描方式"])

    with tab1:
        if processor.main_df is not None:
            st.dataframe(processor.main_df.head(10), use_container_width=True)

    with tab2:
        if processor.ct_scan_df is not None:
            st.dataframe(processor.ct_scan_df, use_container_width=True)

    with tab3:
        if processor.mr_scan_df is not None:
            st.dataframe(processor.mr_scan_df, use_container_width=True)

def show_three_level_dict():
    """显示三级部位字典表生成步骤"""
    st.header("🏗️ 步骤2：三级部位字典表生成")

    processor = st.session_state.processor
    three_level_dict = processor.generate_three_level_dict()

    if three_level_dict is None:
        st.error("无法生成三级部位字典表")
        return

    st.success(f"成功生成三级部位字典表，共 {len(three_level_dict)} 条记录")

    # 分级查看功能
    st.subheader("分级查看")
    level_option = st.selectbox("选择查看级别", ["全部", "一级部位", "二级部位", "三级部位"])

    if level_option == "一级部位":
        level1_summary = three_level_dict.groupby('一级部位').agg({
            '三级部位': 'count',
            'CT适用': lambda x: (x == '是').sum(),
            'MR适用': lambda x: (x == '是').sum()
        }).rename(columns={'三级部位': '三级部位数量', 'CT适用': 'CT适用数', 'MR适用': 'MR适用数'})
        st.dataframe(level1_summary, use_container_width=True)

    elif level_option == "二级部位":
        level2_summary = three_level_dict.groupby(['一级部位', '二级部位']).agg({
            '三级部位': 'count',
            'CT适用': lambda x: (x == '是').sum(),
            'MR适用': lambda x: (x == '是').sum()
        }).rename(columns={'三级部位': '三级部位数量', 'CT适用': 'CT适用数', 'MR适用': 'MR适用数'})
        st.dataframe(level2_summary, use_container_width=True)

    else:
        # 搜索功能
        search_term = st.text_input("搜索部位名称")
        if search_term:
            filtered_dict = three_level_dict[
                three_level_dict['一级部位'].str.contains(search_term, na=False) |
                three_level_dict['二级部位'].str.contains(search_term, na=False) |
                three_level_dict['三级部位'].str.contains(search_term, na=False)
            ]
            st.dataframe(filtered_dict, use_container_width=True)
        else:
            st.dataframe(three_level_dict, use_container_width=True)

    # 数据质量检查
    st.subheader("数据质量检查")
    col1, col2, col3 = st.columns(3)

    with col1:
        valid_codes = three_level_dict['部位编码'].str.len() == 6
        st.metric("有效编码数", valid_codes.sum())

    with col2:
        duplicate_codes = three_level_dict['部位编码'].duplicated().sum()
        st.metric("重复编码数", duplicate_codes)

    with col3:
        empty_names = three_level_dict['三级部位'].isna().sum()
        st.metric("空名称数", empty_names)

    # 下载功能
    if st.button("下载三级部位字典表"):
        csv = three_level_dict.to_csv(index=False, encoding='utf-8-sig')
        st.download_button(
            label="下载CSV文件",
            data=csv,
            file_name=f"三级部位字典表_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
            mime="text/csv"
        )

def show_scan_dict():
    """显示扫描方式字典表生成步骤"""
    st.header("🔬 步骤3：扫描方式字典表生成")

    processor = st.session_state.processor

    # 选择模态
    modality = st.selectbox("选择检查模态", ["CT", "MR"])

    scan_dict = processor.generate_scan_dict(modality)

    if scan_dict is None:
        st.error(f"无法生成{modality}扫描方式字典表")
        return

    st.success(f"成功生成{modality}扫描方式字典表，共 {len(scan_dict)} 条记录")

    # 显示清理前后对比
    st.subheader("扫描方式清理效果")
    if modality == "CT":
        comparison_cols = ['扫描名称', '清理后名称']
    else:
        comparison_cols = ['成像名称', '清理后名称']

    st.dataframe(scan_dict[comparison_cols], use_container_width=True)

    # 完整字典表
    st.subheader(f"{modality}扫描方式字典表")
    st.dataframe(scan_dict, use_container_width=True)

    # 编码分布
    st.subheader("编码分布")
    if modality == "CT":
        code_col = '扫描编码'
    else:
        code_col = '成像编码'

    if code_col in scan_dict.columns:
        code_counts = scan_dict[code_col].value_counts()
        if len(code_counts) > 0:
            fig = px.bar(
                x=[str(x) for x in code_counts.index],
                y=[int(y) for y in code_counts.values],
                title=f"{modality}扫描编码分布",
                labels={'x': '扫描编码', 'y': '数量'}
            )
            st.plotly_chart(fig, use_container_width=True)
        else:
            st.info("暂无编码分布数据")
    else:
        st.warning(f"未找到{code_col}列")

    # 下载功能
    if st.button(f"下载{modality}扫描方式字典表"):
        csv = scan_dict.to_csv(index=False, encoding='utf-8-sig')
        st.download_button(
            label="下载CSV文件",
            data=csv,
            file_name=f"{modality}扫描方式字典表_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
            mime="text/csv"
        )

def show_item_generation():
    """显示检查项目清单生成步骤"""
    st.header("📋 步骤4：检查项目清单生成")

    processor = st.session_state.processor

    # 生成CT和MR项目
    with st.spinner("正在生成检查项目..."):
        ct_items = generate_check_items(processor, 'CT')
        mr_items = generate_check_items(processor, 'MR')

    if ct_items is None or mr_items is None:
        st.error("检查项目生成失败")
        return

    # 显示生成结果统计
    col1, col2, col3 = st.columns(3)
    with col1:
        st.metric("CT项目数量", len(ct_items))
    with col2:
        st.metric("MR项目数量", len(mr_items))
    with col3:
        st.metric("总项目数量", len(ct_items) + len(mr_items))

    # 选择查看的项目类型
    item_type = st.selectbox("选择查看项目类型", ["CT检查项目", "MR检查项目"])

    if item_type == "CT检查项目":
        st.subheader("CT检查项目清单")
        st.dataframe(ct_items, use_container_width=True)

        # CT项目统计
        st.subheader("CT项目统计")
        ct_stats = generate_statistics(ct_items, 'CT')
        st.dataframe(ct_stats, use_container_width=True)

    else:
        st.subheader("MR检查项目清单")
        st.dataframe(mr_items, use_container_width=True)

        # MR项目统计
        st.subheader("MR项目统计")
        mr_stats = generate_statistics(mr_items, 'MR')
        st.dataframe(mr_stats, use_container_width=True)

    # 项目名称格式示例
    st.subheader("项目格式示例")
    col1, col2 = st.columns(2)

    with col1:
        st.write("**CT项目示例：**")
        if len(ct_items) > 0:
            for i, (_, row) in enumerate(ct_items.head(3).iterrows()):
                st.code(f"{row['检查项目编码']} - {row['检查项目名称']}")

    with col2:
        st.write("**MR项目示例：**")
        if len(mr_items) > 0:
            for i, (_, row) in enumerate(mr_items.head(3).iterrows()):
                st.code(f"{row['检查项目编码']} - {row['检查项目名称']}")

    # 导出Excel功能
    if st.button("生成完整Excel报告"):
        excel_buffer = generate_excel_report(ct_items, mr_items, processor)
        if excel_buffer:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            st.download_button(
                label="下载Excel报告",
                data=excel_buffer.getvalue(),
                file_name=f"检查项目清单_{timestamp}.xlsx",
                mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            )

def show_quality_control():
    """显示数据分析与质量控制步骤"""
    st.header("🔍 步骤5：数据分析与质量控制")

    processor = st.session_state.processor

    # 生成项目用于质量控制
    ct_items = generate_check_items(processor, 'CT')
    mr_items = generate_check_items(processor, 'MR')

    if ct_items is None or mr_items is None:
        st.error("无法进行质量控制分析")
        return

    # 综合统计报告
    st.subheader("综合统计报告")

    total_stats = {
        "统计项目": [
            "CT项目总数", "MR项目总数", "总项目数",
            "涉及一级部位数", "涉及CT扫描方式数", "涉及MR扫描方式数"
        ],
        "统计值": [
            len(ct_items), len(mr_items), len(ct_items) + len(mr_items),
            len(ct_items['一级部位'].unique()) if len(ct_items) > 0 else 0,
            len(ct_items['扫描方式清理名'].unique()) if len(ct_items) > 0 else 0,
            len(mr_items['扫描方式清理名'].unique()) if len(mr_items) > 0 else 0
        ],
        "统计时间": [datetime.now().strftime("%Y-%m-%d %H:%M:%S")] * 6
    }

    stats_df = pd.DataFrame(total_stats)
    st.dataframe(stats_df, use_container_width=True)

    # 错误分析
    st.subheader("错误分析")

    errors = []

    # 检查编码格式错误
    if len(ct_items) > 0:
        invalid_ct_codes = ct_items[ct_items['检查项目编码'].str.len() != 10]
        if len(invalid_ct_codes) > 0:
            errors.append(f"CT编码格式错误: {len(invalid_ct_codes)}项")

    if len(mr_items) > 0:
        invalid_mr_codes = mr_items[mr_items['检查项目编码'].str.len() != 10]
        if len(invalid_mr_codes) > 0:
            errors.append(f"MR编码格式错误: {len(invalid_mr_codes)}项")

    # 检查重复编码
    all_codes = []
    if len(ct_items) > 0:
        all_codes.extend(ct_items['检查项目编码'].tolist())
    if len(mr_items) > 0:
        all_codes.extend(mr_items['检查项目编码'].tolist())

    duplicate_codes = len(all_codes) - len(set(all_codes))
    if duplicate_codes > 0:
        errors.append(f"重复编码: {duplicate_codes}项")

    if errors:
        for error in errors:
            st.error(error)
    else:
        st.success("未发现数据质量问题")

    # 数据完整性验证
    st.subheader("数据完整性验证")

    completeness_checks = []

    if len(ct_items) > 0:
        ct_missing = ct_items.isnull().sum().sum()
        completeness_checks.append(("CT项目数据", "完整" if ct_missing == 0 else f"缺失{ct_missing}个值"))

    if len(mr_items) > 0:
        mr_missing = mr_items.isnull().sum().sum()
        completeness_checks.append(("MR项目数据", "完整" if mr_missing == 0 else f"缺失{mr_missing}个值"))

    completeness_df = pd.DataFrame(completeness_checks, columns=["检查项", "状态"])
    st.dataframe(completeness_df, use_container_width=True)

    # 部位覆盖分析
    st.subheader("部位覆盖分析")

    if processor.main_df is not None:
        total_parts = len(processor.main_df)
        ct_covered = len(ct_items['部位编码'].unique()) if len(ct_items) > 0 else 0
        mr_covered = len(mr_items['部位编码'].unique()) if len(mr_items) > 0 else 0

        coverage_data = {
            "模态": ["CT", "MR"],
            "覆盖部位数": [ct_covered, mr_covered],
            "总部位数": [total_parts, total_parts],
            "覆盖率": [f"{ct_covered/total_parts*100:.1f}%" if total_parts > 0 else "0%",
                     f"{mr_covered/total_parts*100:.1f}%" if total_parts > 0 else "0%"]
        }

        coverage_df = pd.DataFrame(coverage_data)
        st.dataframe(coverage_df, use_container_width=True)

def generate_check_items(processor, modality):
    """生成检查项目"""
    if processor.main_df is None:
        return None

    items = []

    # 创建扫描方式映射
    ct_mapping, mr_mapping = create_scan_mapping(processor)
    mapping = ct_mapping if modality == 'CT' else mr_mapping

    # 获取相关列
    if modality == 'CT':
        scan_columns = [col for col in processor.main_df.columns if col.startswith('CT') and col != 'CT']
    else:
        scan_columns = [col for col in processor.main_df.columns if col.startswith('MR') and col != 'MR']

    for _, row in processor.main_df.iterrows():
        # 检查是否有对应模态标记
        if pd.notna(row.get(modality)) and str(row.get(modality)).strip() in ['1', '１']:
            part_name = processor.clean_part_name(row['三级部位'])
            part_code = str(row['部位编码']).strip()

            # 检查每个扫描方式
            for scan_col in scan_columns:
                if pd.notna(row[scan_col]) and row[scan_col] == 1.0:
                    scan_raw_name = scan_col
                    scan_clean_name = processor.clean_scan_name(scan_raw_name, modality)

                    # 查找扫描编码
                    scan_code = find_scan_code(scan_clean_name, mapping, modality)

                    # 生成项目名称和编码
                    item_name = f"{modality}{part_name}({scan_clean_name})"
                    item_code = f"{modality}{part_code}{scan_code}".replace(' ', '')

                    item = {
                        '一级部位': row['一级部位'],
                        '二级编码': row['二级编码'],
                        '三级编码': row['三级编码'],
                        '三级部位': part_name,
                        '部位编码': part_code,
                        '扫描方式原名': scan_raw_name,
                        '扫描方式清理名': scan_clean_name,
                        '扫描编码': scan_code,
                        '检查项目名称': item_name,
                        '检查项目编码': item_code,
                        '检查模态': modality
                    }

                    items.append(item)

    return pd.DataFrame(items)

def create_scan_mapping(processor):
    """创建扫描方式映射表"""
    # CT扫描方式映射
    ct_mapping = {}
    if processor.ct_scan_df is not None:
        for _, row in processor.ct_scan_df.iterrows():
            scan_name = row.get('CT扫描名称', '')
            scan_code = row.get('CT扫描编码', '')
            if pd.notna(scan_name) and pd.notna(scan_code):
                ct_mapping[scan_name] = str(scan_code).zfill(2)

    # MR扫描方式映射
    mr_mapping = {}
    if processor.mr_scan_df is not None:
        for _, row in processor.mr_scan_df.iterrows():
            scan_name = row.get('MR成像名称', '')
            scan_code = row.get('MR成像编码', '')
            if pd.notna(scan_name) and pd.notna(scan_code):
                mr_mapping[scan_name] = str(scan_code).zfill(2)

    return ct_mapping, mr_mapping

def find_scan_code(scan_clean_name, mapping, modality):
    """查找扫描编码"""
    # 直接匹配
    for key, code in mapping.items():
        if scan_clean_name in key or key in scan_clean_name:
            return code

    # 默认编码规则
    if modality == 'CT':
        default_codes = {
            '平扫': '10', '增强': '20', 'CTA': '30', 'CTV': '31', '灌注': '40'
        }
    else:
        default_codes = {
            '平扫': '10', '增强': '20', 'MRA': '30', 'MRV': '31'
        }

    for key, code in default_codes.items():
        if key in scan_clean_name:
            return code

    return '99'  # 未知类型

def generate_statistics(items_df, modality):
    """生成统计信息"""
    if len(items_df) == 0:
        return pd.DataFrame()

    stats = []

    # 按一级部位统计
    part_stats = items_df['一级部位'].value_counts()
    for part, count in part_stats.items():
        stats.append({
            '统计类型': '一级部位',
            '统计项目': part,
            '数量': count,
            '占比': f"{count/len(items_df)*100:.1f}%"
        })

    # 按扫描方式统计
    scan_stats = items_df['扫描方式清理名'].value_counts()
    for scan, count in scan_stats.items():
        stats.append({
            '统计类型': '扫描方式',
            '统计项目': scan,
            '数量': count,
            '占比': f"{count/len(items_df)*100:.1f}%"
        })

    return pd.DataFrame(stats)

def generate_excel_report(ct_items, mr_items, processor):
    """生成Excel报告"""
    try:
        buffer = BytesIO()

        with pd.ExcelWriter(buffer, engine='openpyxl') as writer:
            # CT检查项目清单
            ct_items.to_excel(writer, sheet_name='CT检查项目清单', index=False)

            # MR检查项目清单
            mr_items.to_excel(writer, sheet_name='MR检查项目清单', index=False)

            # 字段字典
            field_dict = generate_field_dictionary()
            field_dict.to_excel(writer, sheet_name='字段字典', index=False)

            # CT统计信息
            if len(ct_items) > 0:
                ct_stats = generate_statistics(ct_items, 'CT')
                ct_stats.to_excel(writer, sheet_name='CT统计信息', index=False)

            # MR统计信息
            if len(mr_items) > 0:
                mr_stats = generate_statistics(mr_items, 'MR')
                mr_stats.to_excel(writer, sheet_name='MR统计信息', index=False)

        buffer.seek(0)
        return buffer

    except Exception as e:
        st.error(f"生成Excel报告失败：{str(e)}")
        return None

def generate_field_dictionary():
    """生成字段字典表"""
    field_dict = {
        '字段名称': [
            '一级部位', '二级编码', '三级编码', '三级部位', '部位编码',
            '扫描方式原名', '扫描方式清理名', '扫描编码', '检查项目名称', '检查项目编码', '检查模态'
        ],
        '字段类型': ['文本'] * 11,
        '字段描述': [
            '一级部位名称', '二级部位编码', '三级部位编码', '三级部位名称', '六位部位编码',
            '原始扫描方式名称', '清理后扫描方式名称', '两位扫描编码', '最终生成的项目名称', '最终生成的项目编码', '检查模态类型'
        ],
        '示例值': [
            '头部', '01', '01', '颅脑', '010101',
            'CT-平扫', '平扫', '10', 'CT颅脑(平扫)', 'CT01010110', 'CT'
        ],
        '数据格式': [
            '中文名称', '两位数字', '两位数字', '中文名称', '六位数字',
            '原始列名', '简化名称', '两位数字', '模态+部位+(扫描)', '模态+部位编码+扫描编码', 'CT或MR'
        ],
        '是否必填': ['是'] * 11,
        '备注': [
            '医学标准术语', '部位层级编码', '部位层级编码', '具体部位名称', '唯一标识编码',
            '数据表原始列名', '去除前缀后名称', '扫描方式编码', '标准格式名称', '10位唯一编码', '检查模态标识'
        ]
    }

    return pd.DataFrame(field_dict)

if __name__ == "__main__":
    main()
