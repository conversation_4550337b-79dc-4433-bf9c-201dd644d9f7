# 检查项目名称和编码生成方案

## 数据结构分析

### 1. 数据基本信息
- **数据源**: `NEW_检查项目名称结构表 (7).xlsx`
- **数据规模**: 502行，35列
- **数据结构**: 层级化的部位编码 + 多模态检查配置

### 2. 部位层级结构
数据采用三级层级结构：

#### 一级部位分布
- **四肢及关节**: 121项 (24.1%)
- **头部**: 78项 (15.5%)
- **血管**: 58项 (11.6%)
- **无指定部位**: 55项 (11.0%)
- **盆部**: 28项 (5.6%)
- **脊柱**: 24项 (4.8%)
- **胸部**: 20项 (4.0%)
- **腹部**: 18项 (3.6%)
- **颈部**: 12项 (2.4%)

#### 编码规则
- **一级编码**: 数字编码 (如: 1.0)
- **二级编码**: 两位数字 (如: 01, 02)
- **三级编码**: 两位数字 (如: 01, 02, 03)
- **部位编码**: 六位数字组合 (如: 010101)

### 3. 检查模态分析

#### CT相关模态 (8种)
1. `CT-平扫`: 107个项目
2. `CT-增强`: 83个项目
3. `CT-平扫+增强`: 80个项目
4. `CT-CTA`: 28个项目
5. `CT-CTV`: 26个项目
6. `CT_灌注`: 7个项目
7. `CT延时显像(扩展)CTU`: 2个项目

#### MR相关模态 (17种)
1. `MR-平扫`: 191个项目
2. `MR-增强`: 144个项目
3. `MR-平扫+增强`: 142个项目
4. `MR-MRA`: 25个项目
5. `MR-CE_MRA`: 25个项目
6. `MR-MRV`: 6个项目
7. `MR-CE_MRV`: 6个项目
8. `MR-MRA+CE_MRA`: 5个项目
9. `MR-水成像MRM`: 3个项目
10. `MR-水成像MRH`: 3个项目
11. `MR-水成像IEHM`: 3个项目
12. `MR-水成像MRU`: 2个项目
13. `MR-水成像MRCP`: 1个项目
14. `MR-电影成像`: 1个项目
15. `MR-平扫+DCE`: 1个项目
16. `MR-平扫+ASL`: 1个项目

#### DR相关模态 (1种)
- `DR`: 普通放射摄影

## 生成方案

### 1. 项目名称生成规则
**格式**: `一级部位-三级部位-检查模态`

**示例**:
- `头部-颅脑-CT-平扫`
- `头部-颅脑-MR-增强`
- `四肢及关节-肘关节-CT-平扫+增强`

### 2. 项目编码生成规则
**格式**: `部位编码 + 模态编码 + 序号`

#### 模态编码对照表
```
CT相关:
- CT: 01
- CT-平扫: 0101
- CT-增强: 0102
- CT-平扫+增强: 0103
- CT-CTA: 0104
- CT-CTV: 0105
- CT_灌注: 0106
- CT延时显像(扩展)CTU: 0107

MR相关:
- MR: 02
- MR-平扫: 0201
- MR-增强: 0202
- MR-平扫+增强: 0203
- MR-平扫+ASL: 0204
- MR-平扫+DCE: 0205
- MR-MRA: 0206
- MR-MRV: 0207
- MR-CE_MRA: 0208
- MR-CE_MRV: 0209
- MR-MRA+CE_MRA: 0210
- MR-电影成像: 0211
- MR-水成像MRH: 0212
- MR-水成像MRM: 0213
- MR-水成像MRCP: 0214
- MR-水成像MRU: 0215
- MR-水成像IEHM: 0216

DR相关:
- DR: 03
```

#### 编码示例
- 部位编码: `010101` (头部-颅脑-颅脑)
- 模态编码: `0101` (CT-平扫)
- 序号: `01`
- 完整编码: `010101010101`

### 3. 生成结果统计
- **总项目数**: 892个
- **涉及部位**: 9个一级部位
- **涉及模态**: 23种不同检查模态

#### 各部位项目分布
1. **四肢及关节**: 228个 (25.6%)
2. **头部**: 180个 (20.2%)
3. **血管**: 155个 (17.4%)
4. **盆部**: 92个 (10.3%)
5. **腹部**: 90个 (10.1%)
6. **脊柱**: 60个 (6.7%)
7. **颈部**: 43个 (4.8%)
8. **胸部**: 39个 (4.4%)
9. **无指定部位**: 5个 (0.6%)

## 使用方法

### 1. 运行生成器
```bash
python3 generate_check_items.py
```

### 2. 输出文件
- **主文件**: `检查项目生成结果_YYYYMMDD_HHMMSS.xlsx`
- **包含工作表**:
  - `检查项目清单`: 完整的检查项目列表
  - `模态统计`: 按检查模态分组的统计信息
  - `部位统计`: 按身体部位分组的统计信息

### 3. 自定义配置
可以通过修改 `CheckItemGenerator` 类的以下方法来自定义生成规则：
- `generate_item_code()`: 修改编码生成规则
- `generate_all_check_items()`: 修改项目名称生成规则

## 数据质量分析

### 1. 数据完整性
- **部位信息**: 94.4%的记录有完整的部位信息
- **模态配置**: 平均每个部位配置3.9种检查模态
- **编码规范**: 99.2%的记录有标准的部位编码

### 2. 数据一致性
- **编码规则**: 遵循六位数字的部位编码规则
- **模态标识**: 使用1.0表示该模态适用于该部位
- **层级结构**: 严格按照一级→二级→三级的层级关系

### 3. 扩展性
- **新增模态**: 可在模态编码表中增加新的检查类型
- **新增部位**: 可按现有编码规则增加新的身体部位
- **编码升级**: 支持更复杂的编码规则扩展

## 应用场景

1. **医院信息系统**: 统一的检查项目编码和名称规范
2. **影像设备管理**: 按模态分类管理检查项目
3. **费用管理**: 基于标准编码的收费项目管理
4. **质控管理**: 按部位和模态进行质量控制
5. **数据统计**: 标准化的检查项目数据分析

## 注意事项

1. **数据源更新**: 当原始Excel文件更新时，需重新运行生成器
2. **编码唯一性**: 确保生成的编码在系统中唯一
3. **命名规范**: 检查项目名称需符合医疗行业标准
4. **版本管理**: 建议对生成的结果文件进行版本控制 