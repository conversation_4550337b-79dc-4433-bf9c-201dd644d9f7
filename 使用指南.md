# 医学检查项目处理流程使用指南

## 🚀 快速开始

### 方式一：交互式Web界面（推荐）
```bash
# 1. 安装依赖
pip install -r requirements.txt

# 2. 启动应用
streamlit run streamlit_medical_pipeline.py

# 3. 在浏览器中打开
http://localhost:8501
```

### 方式二：命令行演示
```bash
# 直接运行演示脚本
python demo_script.py
```

## 📋 处理步骤详解

### 步骤1：数据加载与分析
- **功能**：读取Excel源文件，进行基本统计分析
- **输入**：包含"三级部位结构"、"CT扫描方式"、"MR扫描方式"工作表的Excel文件
- **输出**：数据统计信息、部位分布图表、模态分布分析

**关键指标**：
- 主数据表：502行 × 35列
- CT适用部位：163个
- MR适用部位：196个
- 缺失值处理和数据类型分析

### 步骤2：三级部位字典表生成
- **功能**：清洗和验证部位数据，生成标准化字典表
- **处理规则**：
  - 验证部位编码格式（六位数字）
  - 清理部位名称（去除多余空格）
  - 标记CT/MR适用性
- **输出**：标准化的三级部位字典表（406条有效记录）

**数据结构**：
```
一级编码 | 一级部位 | 二级编码 | 二级部位 | 三级编码 | 三级部位 | 部位编码 | CT适用 | MR适用
```

### 步骤3：扫描方式字典表生成
- **功能**：处理CT和MR扫描方式，应用清理规则
- **清理规则**：
  - 去除"CT-"、"CT_"、"CT"前缀
  - 去除"MR-"、"MR_"、"MR"前缀
  - 标准化扫描方式名称
- **输出**：
  - CT扫描方式字典表（57条记录）
  - MR扫描方式字典表（54条记录）

**示例转换**：
```
CT-平扫 → 平扫
MR-增强 → 增强
CT_灌注 → 灌注
```

### 步骤4：检查项目清单生成
- **功能**：根据规则生成标准化的检查项目名称和编码
- **生成规则**：
  - 项目名称格式：`[模态][部位]([扫描方式])`
  - 项目编码格式：`[模态][部位编码][扫描编码]`（10位）
- **输出**：
  - CT检查项目清单（332个项目）
  - MR检查项目清单（555个项目）

**项目示例**：
```
CT01010110 - CT颅脑(平扫)
CT01010120 - CT颅脑(增强)
MR01010130 - MR颅脑(平扫+增强)
```

### 步骤5：数据分析与质量控制
- **功能**：统计报告、错误分析、完整性验证
- **质量检查**：
  - 编码格式验证（10位长度）
  - 重复编码检测
  - 数据完整性验证
  - 部位覆盖分析
- **输出**：质量控制报告和错误分析

## 📊 输出文件说明

### Excel报告结构
生成的Excel文件包含以下工作表：

1. **CT检查项目清单**
   - 完整的CT项目列表
   - 包含项目编码、名称、部位信息、扫描方式等

2. **MR检查项目清单**
   - 完整的MR项目列表
   - 包含项目编码、名称、部位信息、扫描方式等

3. **三级部位字典**
   - 标准化的部位层级结构
   - 包含编码验证和适用性标记

4. **CT扫描方式字典**
   - CT扫描方式编码对照表
   - 包含清理前后对比

5. **MR扫描方式字典**
   - MR扫描方式编码对照表
   - 包含清理前后对比

### 数据列说明
```
检查项目清单字段：
- 项目编码：10位唯一标识（如：CT01010110）
- 项目名称：标准格式名称（如：CT颅脑(平扫)）
- 模态类型：CT或MR
- 一级部位：一级部位名称
- 三级部位：具体部位名称
- 部位编码：6位部位编码
- 扫描方式：清理后的扫描方式名称
- 扫描编码：2位扫描编码
```

## 🔧 配置和自定义

### 扫描编码规则
可以通过修改默认编码规则来适应不同需求：

**CT默认编码**：
- 平扫：10
- 增强：20
- CTA：30
- CTV：31
- 灌注：40

**MR默认编码**：
- 平扫：10
- 增强：20
- MRA：30
- MRV：31

### 清理规则自定义
在`clean_scan_name()`方法中可以修改：
- 前缀去除规则
- 特殊字符处理
- 名称标准化规则

## ⚠️ 注意事项

### 数据质量要求
1. **Excel文件结构**：必须包含指定的工作表和列
2. **编码格式**：部位编码必须是6位数字
3. **数据完整性**：关键字段不能为空
4. **编码唯一性**：生成的项目编码应保持唯一

### 常见问题处理
1. **重复编码**：当前检测到11项重复编码，需要手动检查和调整
2. **缺失值**：系统会自动处理NaN值，但建议检查源数据质量
3. **格式错误**：确保部位编码为6位数字格式

### 性能优化
- 大数据集处理时建议使用命令行版本
- Web界面适合交互式分析和小规模数据
- 定期清理生成的临时文件

## 📞 技术支持

### 系统要求
- Python 3.7+
- 内存：建议2GB以上
- 磁盘空间：100MB以上

### 依赖包版本
```
streamlit>=1.28.0
pandas>=2.0.0
numpy>=1.24.0
plotly>=5.15.0
openpyxl>=3.1.0
```

### 故障排除
1. **导入错误**：检查依赖包是否正确安装
2. **文件读取失败**：确认Excel文件格式和工作表名称
3. **内存不足**：尝试使用命令行版本或分批处理
4. **编码问题**：确保文件使用UTF-8编码

## 🎯 最佳实践

1. **数据备份**：处理前备份原始数据文件
2. **版本控制**：对生成结果进行版本管理
3. **质量检查**：每次生成后检查质量控制报告
4. **定期更新**：当源数据更新时及时重新处理
5. **文档记录**：记录自定义配置和处理参数

---

**版本**：v1.0  
**更新时间**：2025-07-05  
**适用范围**：医学影像检查项目标准化处理
