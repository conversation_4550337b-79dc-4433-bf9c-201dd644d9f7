#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查项目名称和编码生成器 V2.0
根据新的生成规则：
1. 按模态分别生成CT，MR的检查项目清单
2. 扫描方式编码采用两位，从CT扫描方式、MR扫描方式工作表中获取
3. 名称格式："CT"+"部位"+"("扫描")"
4. 编码格式："CT"+"部位编码"+"扫描编码"
5. 去除多余空格，扫描方式去除"CT-"重复字符
6. 生成字段字典表
"""

import pandas as pd
import numpy as np
from datetime import datetime
import os
import re

class CheckItemGeneratorV2:
    """检查项目生成器V2类"""
    
    def __init__(self, excel_file):
        """初始化生成器"""
        self.excel_file = excel_file
        self.main_df = None
        self.ct_scan_df = None
        self.mr_scan_df = None
        self.load_data()
        
    def load_data(self):
        """加载Excel数据"""
        try:
            # 加载主数据表
            self.main_df = pd.read_excel(self.excel_file, sheet_name='三级部位结构')
            print(f"成功加载主数据：{len(self.main_df)}行，{len(self.main_df.columns)}列")
            
            # 加载CT扫描方式表
            self.ct_scan_df = pd.read_excel(self.excel_file, sheet_name='CT扫描方式')
            print(f"成功加载CT扫描方式：{len(self.ct_scan_df)}行")
            
            # 加载MR扫描方式表
            self.mr_scan_df = pd.read_excel(self.excel_file, sheet_name='MR扫描方式')
            print(f"成功加载MR扫描方式：{len(self.mr_scan_df)}行")
            
        except Exception as e:
            print(f"加载数据失败：{e}")
            raise
    
    def create_scan_mapping(self):
        """创建扫描方式映射表"""
        # CT扫描方式映射
        ct_mapping = {}
        for _, row in self.ct_scan_df.iterrows():
            scan_name = row['CT扫描名称']
            scan_code = row['CT扫描编码']
            if pd.notna(scan_name) and pd.notna(scan_code):
                ct_mapping[scan_name] = str(scan_code).zfill(2)  # 确保两位编码
        
        # MR扫描方式映射
        mr_mapping = {}
        for _, row in self.mr_scan_df.iterrows():
            scan_name = row['MR成像名称']
            scan_code = row['MR成像编码']
            if pd.notna(scan_name) and pd.notna(scan_code):
                mr_mapping[scan_name] = str(scan_code).zfill(2)  # 确保两位编码
        
        return ct_mapping, mr_mapping
    
    def clean_scan_name(self, scan_name, modality):
        """清理扫描方式名称"""
        if pd.isna(scan_name):
            return ""
        
        # 转换为字符串并清理空格
        scan_name = str(scan_name).strip()
        
        # 去除重复字符
        if modality == 'CT':
            scan_name = scan_name.replace('CT-', '').replace('CT_', '').replace('CT', '')
        elif modality == 'MR':
            scan_name = scan_name.replace('MR-', '').replace('MR_', '').replace('MR', '')
        
        # 去除多余空格
        scan_name = re.sub(r'\s+', ' ', scan_name).strip()
        
        return scan_name
    
    def clean_part_name(self, part_name):
        """清理部位名称"""
        if pd.isna(part_name):
            return ""
        
        # 转换为字符串并清理空格
        part_name = str(part_name).strip()
        
        # 去除多余空格
        part_name = re.sub(r'\s+', ' ', part_name)
        
        return part_name
    
    def generate_ct_items(self):
        """生成CT检查项目"""
        ct_items = []
        ct_mapping, _ = self.create_scan_mapping()
        
        # 获取CT相关列
        ct_columns = [col for col in self.main_df.columns if col.startswith('CT') and col != 'CT']
        
        for index, row in self.main_df.iterrows():
            # 检查是否有CT标记
            if pd.notna(row.get('CT')) and str(row.get('CT')).strip() in ['1', '１']:
                part_name = self.clean_part_name(row['三级部位'])
                part_code = str(row['部位编码']).strip()
                
                # 检查每个CT扫描方式
                for ct_col in ct_columns:
                    if pd.notna(row[ct_col]) and row[ct_col] == 1.0:
                        # 匹配扫描方式
                        scan_raw_name = ct_col
                        scan_clean_name = self.clean_scan_name(scan_raw_name, 'CT')
                        
                        # 寻找匹配的扫描编码
                        scan_code = self.find_scan_code(scan_clean_name, ct_mapping, 'CT')
                        
                        # 生成项目名称和编码
                        item_name = f"CT{part_name}({scan_clean_name})"
                        item_code = f"CT{part_code}{scan_code}".replace(' ', '')
                        
                        ct_item = {
                            '一级部位': row['一级部位'],
                            '二级编码': row['二级编码'],
                            '三级编码': row['三级编码'],
                            '三级部位': part_name,
                            '部位编码': part_code,
                            '扫描方式原名': scan_raw_name,
                            '扫描方式清理名': scan_clean_name,
                            '扫描编码': scan_code,
                            '检查项目名称': item_name,
                            '检查项目编码': item_code,
                            '检查模态': 'CT'
                        }
                        
                        ct_items.append(ct_item)
        
        return pd.DataFrame(ct_items)
    
    def generate_mr_items(self):
        """生成MR检查项目"""
        mr_items = []
        _, mr_mapping = self.create_scan_mapping()
        
        # 获取MR相关列
        mr_columns = [col for col in self.main_df.columns if col.startswith('MR') and col != 'MR']
        
        for index, row in self.main_df.iterrows():
            # 检查是否有MR标记
            if pd.notna(row.get('MR')) and str(row.get('MR')).strip() == '1':
                part_name = self.clean_part_name(row['三级部位'])
                part_code = str(row['部位编码']).strip()
                
                # 检查每个MR扫描方式
                for mr_col in mr_columns:
                    if pd.notna(row[mr_col]) and row[mr_col] == 1.0:
                        # 匹配扫描方式
                        scan_raw_name = mr_col
                        scan_clean_name = self.clean_scan_name(scan_raw_name, 'MR')
                        
                        # 寻找匹配的扫描编码
                        scan_code = self.find_scan_code(scan_clean_name, mr_mapping, 'MR')
                        
                        # 生成项目名称和编码
                        item_name = f"MR{part_name}({scan_clean_name})"
                        item_code = f"MR{part_code}{scan_code}".replace(' ', '')
                        
                        mr_item = {
                            '一级部位': row['一级部位'],
                            '二级编码': row['二级编码'],
                            '三级编码': row['三级编码'],
                            '三级部位': part_name,
                            '部位编码': part_code,
                            '扫描方式原名': scan_raw_name,
                            '扫描方式清理名': scan_clean_name,
                            '扫描编码': scan_code,
                            '检查项目名称': item_name,
                            '检查项目编码': item_code,
                            '检查模态': 'MR'
                        }
                        
                        mr_items.append(mr_item)
        
        return pd.DataFrame(mr_items)
    
    def find_scan_code(self, scan_name, mapping, modality):
        """查找扫描方式编码"""
        # 直接匹配
        if scan_name in mapping:
            return mapping[scan_name]
        
        # 模糊匹配
        for map_name, code in mapping.items():
            if pd.notna(map_name) and pd.notna(scan_name):
                if scan_name in map_name or map_name in scan_name:
                    return code
        
        # 特殊处理一些常见情况
        if modality == 'CT':
            if '平扫' in scan_name:
                return '10'  # CT平扫默认编码
            elif '增强' in scan_name:
                return '20'  # CT增强默认编码
            elif 'CTA' in scan_name:
                return '30'  # CTA默认编码
            elif 'CTV' in scan_name:
                return '31'  # CTV默认编码
            elif '灌注' in scan_name:
                return '40'  # 灌注默认编码
        elif modality == 'MR':
            if '平扫' in scan_name:
                return '10'  # MR平扫默认编码
            elif '增强' in scan_name:
                return '20'  # MR增强默认编码
            elif 'MRA' in scan_name:
                return '30'  # MRA默认编码
            elif 'MRV' in scan_name:
                return '31'  # MRV默认编码
        
        # 如果都找不到，返回默认编码
        return '99'
    
    def generate_field_dictionary(self, ct_df, mr_df):
        """生成字段字典表"""
        field_dict = {
            '字段名': [],
            '字段描述': [],
            '数据类型': [],
            '取值范围': [],
            '示例值': []
        }
        
        # 基础字段
        base_fields = {
            '一级部位': ('一级部位名称', '文本', '头部、四肢及关节、血管等', '头部'),
            '二级编码': ('二级部位编码', '文本', '01-99', '01'),
            '三级编码': ('三级部位编码', '文本', '01-99', '01'),
            '三级部位': ('三级部位名称', '文本', '具体部位名称', '颅脑'),
            '部位编码': ('六位部位编码', '文本', '010101-999999', '010101'),
            '扫描方式原名': ('原始扫描方式名称', '文本', '数据表中的原始列名', 'CT-平扫'),
            '扫描方式清理名': ('清理后扫描方式名称', '文本', '去除前缀后的名称', '平扫'),
            '扫描编码': ('两位扫描编码', '文本', '10-99', '10'),
            '检查项目名称': ('最终生成的项目名称', '文本', '模态+部位+(扫描)', 'CT颅脑(平扫)'),
            '检查项目编码': ('最终生成的项目编码', '文本', '模态+部位编码+扫描编码', 'CT01010110'),
            '检查模态': ('检查模态类型', '文本', 'CT、MR', 'CT')
        }
        
        for field, (desc, dtype, range_val, example) in base_fields.items():
            field_dict['字段名'].append(field)
            field_dict['字段描述'].append(desc)
            field_dict['数据类型'].append(dtype)
            field_dict['取值范围'].append(range_val)
            field_dict['示例值'].append(example)
        
        return pd.DataFrame(field_dict)
    
    def export_results(self, output_file=None):
        """导出生成结果"""
        if output_file is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"检查项目生成结果_V2_{timestamp}.xlsx"
        
        # 生成CT和MR项目
        ct_df = self.generate_ct_items()
        mr_df = self.generate_mr_items()
        
        # 生成字段字典
        field_dict_df = self.generate_field_dictionary(ct_df, mr_df)
        
        # 生成统计信息
        ct_stats = self.generate_statistics(ct_df, 'CT')
        mr_stats = self.generate_statistics(mr_df, 'MR')
        
        # 导出到Excel
        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            # 主要结果
            ct_df.to_excel(writer, sheet_name='CT检查项目清单', index=False)
            mr_df.to_excel(writer, sheet_name='MR检查项目清单', index=False)
            
            # 字段字典
            field_dict_df.to_excel(writer, sheet_name='字段字典', index=False)
            
            # 统计信息
            ct_stats.to_excel(writer, sheet_name='CT统计', index=False)
            mr_stats.to_excel(writer, sheet_name='MR统计', index=False)
        
        print(f"结果已导出到: {output_file}")
        print(f"CT项目数量: {len(ct_df)}")
        print(f"MR项目数量: {len(mr_df)}")
        print(f"总项目数量: {len(ct_df) + len(mr_df)}")
        
        return ct_df, mr_df, field_dict_df
    
    def generate_statistics(self, df, modality):
        """生成统计信息"""
        if len(df) == 0:
            return pd.DataFrame()
        
        stats = []
        
        # 按部位统计
        part_stats = df.groupby('一级部位').agg({
            '检查项目名称': 'count',
            '扫描方式清理名': lambda x: len(x.unique())
        }).rename(columns={
            '检查项目名称': '项目数量',
            '扫描方式清理名': '扫描方式数量'
        })
        
        for part, row in part_stats.iterrows():
            stats.append({
                '统计维度': '一级部位',
                '统计值': part,
                '项目数量': row['项目数量'],
                '扫描方式数量': row['扫描方式数量']
            })
        
        # 按扫描方式统计
        scan_stats = df.groupby('扫描方式清理名').agg({
            '检查项目名称': 'count',
            '一级部位': lambda x: len(x.unique())
        }).rename(columns={
            '检查项目名称': '项目数量',
            '一级部位': '涉及部位数量'
        })
        
        for scan, row in scan_stats.iterrows():
            stats.append({
                '统计维度': '扫描方式',
                '统计值': scan,
                '项目数量': row['项目数量'],
                '涉及部位数量': row['涉及部位数量']
            })
        
        return pd.DataFrame(stats)
    
    def print_summary(self):
        """打印生成摘要"""
        ct_df = self.generate_ct_items()
        mr_df = self.generate_mr_items()
        
        print("\n=== 检查项目生成摘要 V2.0 ===")
        print(f"CT项目数: {len(ct_df)}")
        print(f"MR项目数: {len(mr_df)}")
        print(f"总项目数: {len(ct_df) + len(mr_df)}")
        
        print("\n=== CT项目示例 ===")
        if len(ct_df) > 0:
            for i, (_, item) in enumerate(ct_df.head(5).iterrows()):
                print(f"{i+1}. {item['检查项目编码']} - {item['检查项目名称']}")
        
        print("\n=== MR项目示例 ===")
        if len(mr_df) > 0:
            for i, (_, item) in enumerate(mr_df.head(5).iterrows()):
                print(f"{i+1}. {item['检查项目编码']} - {item['检查项目名称']}")

def main():
    """主函数"""
    excel_file = "NEW_检查项目名称结构表 (7).xlsx"
    
    if not os.path.exists(excel_file):
        print(f"错误: 找不到文件 {excel_file}")
        return
    
    # 创建生成器
    generator = CheckItemGeneratorV2(excel_file)
    
    # 打印生成摘要
    generator.print_summary()
    
    # 导出结果
    generator.export_results()

if __name__ == "__main__":
    main() 